import { createClient } from '@supabase/supabase-js'
import { SuitEnhancedProcedure, enhanceProcedureWithSuit, SuitData } from '@/types/suit-enhanced-procedure'
import { Database } from '@/lib/database.types'

/**
 * Crea un cliente de Supabase directo para el servicio
 */
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  return createClient<Database>(supabaseUrl, supabaseKey)
}

/**
 * Interface para datos consolidados de la vista all_procedures
 * Incluye tanto trámites como OPAs con campos unificados
 */
export interface ConsolidatedProcedureData {
  // Campos base de la vista all_procedures
  type: 'TRAMITE' | 'OPA'
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  dependency_name: string
  dependency_code: string
  subdependency_name?: string
  subdependency_code?: string
  is_active: boolean
  created_at: string
  updated_at: string

  // Campos específicos obtenidos via JOINs
  procedure_code?: string // codigo_tramite para trámites, code para OPAs
  has_cost?: boolean
  cost_description?: string
  suit_link?: string
  govco_link?: string

  // Datos SUIT (solo para trámites)
  descripcion_detallada?: string
  scraping_status?: string

  // Campos adicionales para compatibilidad
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  difficulty_level?: number
  popularity_score?: number
}

/**
 * Obtiene todos los procedimientos (trámites y OPAs) con sus datos SUIT asociados
 * Usa la vista all_procedures como fuente consolidada y complementa con datos específicos
 */
export async function getAllProceduresWithSuit(): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Starting getAllProceduresWithSuit...')
    const supabase = getSupabaseClient()

    // Usar directamente la consulta fallback que sabemos que funciona
    return await getAllProceduresWithSuitFallback()
  } catch (error) {
    console.error('Error in getAllProceduresWithSuit:', error)
    return []
  }
}

/**
 * Función fallback que usa consulta directa cuando la función RPC no está disponible
 */
async function getAllProceduresWithSuitFallback(): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Using enhanced method with real codes')
    const supabase = getSupabaseClient()

    // Obtener trámites con códigos reales
    const { data: tramitesData, error: tramitesError } = await supabase
      .from('procedures')
      .select(`
        id, name, description, cost, response_time, category, is_active,
        created_at, updated_at, codigo_tramite, has_cost, cost_description,
        subdependencies!inner(
          id, name, code,
          dependencies!inner(
            id, name, code, contact_email, contact_phone, address
          )
        )
      `)
      .eq('is_active', true)
      .order('name', { ascending: true })

    if (tramitesError) {
      console.error('Error fetching tramites:', tramitesError)
    }

    // Obtener OPAs con códigos reales (usar tabla opas correcta)
    const { data: opasData, error: opasError } = await supabase
      .from('opas')
      .select(`
        id, code, name, description, created_at, updated_at,
        dependency_id, subdependency_id, has_cost, cost_description,
        suit_link, govco_link, is_active
      `)
      .eq('is_active', true)
      .order('code', { ascending: true })

    if (opasError) {
      console.error('Error fetching opas:', opasError)
    }

    const allProcedures: SuitEnhancedProcedure[] = []

    // Procesar trámites
    if (tramitesData) {
      tramitesData.forEach(item => {
        const dependency = item.subdependencies?.dependencies
        const subdependency = item.subdependencies

        const baseData = {
          id: item.id,
          name: item.name,
          codigo_tramite: item.codigo_tramite, // Usar código real de la base de datos
          code: undefined,
          description: item.description,
          cost: item.cost,
          response_time: item.response_time,
          category: item.category,
          has_cost: item.has_cost || false,
          cost_description: item.cost_description || '',
          suit_link: '', // No disponible en procedures
          govco_link: '', // No disponible en procedures
          procedure_type: 'tramite',
          dependency: {
            id: dependency?.id || '',
            name: dependency?.name || '',
            code: dependency?.code || '',
            contact_email: dependency?.contact_email || undefined,
            contact_phone: dependency?.contact_phone || undefined,
            address: dependency?.address || undefined
          },
          subdependency: subdependency ? {
            id: subdependency.id,
            name: subdependency.name,
            code: subdependency.code,
            dependency_id: dependency?.id || ''
          } : undefined,
          requirements: [],
          documents_required: [],
          process_steps: [],
          legal_framework: '',
          contact_info: null,
          online_available: false,
          tags: [],
          difficulty_level: 1,
          popularity_score: 0,
          is_active: item.is_active,
          created_at: item.created_at,
          updated_at: item.updated_at
        }

        allProcedures.push(enhanceProcedureWithSuit(baseData, undefined))
      })
    }

    // Obtener datos de dependencias y subdependencias para OPAs
    const dependencyMap = new Map()
    const subdependencyMap = new Map()

    if (opasData && opasData.length > 0) {
      // Obtener dependencias únicas
      const dependencyIds = [...new Set(opasData.map(item => item.dependency_id).filter(Boolean))]
      if (dependencyIds.length > 0) {
        const { data: dependencies } = await supabase
          .from('dependencies')
          .select('id, name, code, contact_email, contact_phone, address')
          .in('id', dependencyIds)

        dependencies?.forEach(dep => dependencyMap.set(dep.id, dep))
      }

      // Obtener subdependencias únicas
      const subdependencyIds = [...new Set(opasData.map(item => item.subdependency_id).filter(Boolean))]
      if (subdependencyIds.length > 0) {
        const { data: subdependencies } = await supabase
          .from('subdependencies')
          .select('id, name, code, dependency_id')
          .in('id', subdependencyIds)

        subdependencies?.forEach(sub => subdependencyMap.set(sub.id, sub))
      }
    }

    // Procesar OPAs
    if (opasData) {
      opasData.forEach(item => {
        const dependency = dependencyMap.get(item.dependency_id)
        const subdependency = subdependencyMap.get(item.subdependency_id)

        const baseData = {
          id: item.id,
          name: item.name, // Usar campo name real de la tabla opas
          codigo_tramite: undefined,
          code: item.code, // Usar código real de la base de datos
          description: item.description,
          cost: undefined,
          response_time: undefined,
          category: undefined,
          has_cost: item.has_cost || false, // Usar campo has_cost real de la tabla opas
          cost_description: item.cost_description || '', // Usar campo cost_description real
          suit_link: item.suit_link || '', // Usar campo suit_link real
          govco_link: item.govco_link || '', // Usar campo govco_link real
          procedure_type: 'opa',
          dependency: dependency ? {
            id: dependency.id,
            name: dependency.name,
            code: dependency.code,
            contact_email: dependency.contact_email || undefined,
            contact_phone: dependency.contact_phone || undefined,
            address: dependency.address || undefined
          } : undefined,
          subdependency: subdependency ? {
            id: subdependency.id,
            name: subdependency.name,
            code: subdependency.code,
            dependency_id: dependency?.id || ''
          } : undefined,
          requirements: [],
          documents_required: [],
          process_steps: [],
          legal_framework: '',
          contact_info: null,
          online_available: false,
          tags: [],
          difficulty_level: 1,
          popularity_score: 0,
          is_active: item.is_active,
          created_at: item.created_at,
          updated_at: item.updated_at
        }

        allProcedures.push(enhanceProcedureWithSuit(baseData, undefined))
      })
    }

    console.log(`Successfully processed ${allProcedures.length} procedures (${tramitesData?.length || 0} trámites, ${opasData?.length || 0} OPAs)`)
    return allProcedures

  } catch (error) {
    console.error('Error in getAllProceduresWithSuitFallback:', error)
    return []
  }
}

/**
 * Extrae el ficha_id de un enlace SUIT
 */
function extractFichaIdFromSuitLink(suitLink: string): string {
  try {
    const url = new URL(suitLink)
    const fiParam = url.searchParams.get('fi')
    return fiParam || ''
  } catch {
    return ''
  }
}

/**
 * Obtiene procedimientos (trámites y OPAs) con datos SUIT por dependencia
 * Usa la vista all_procedures como fuente consolidada
 */
export async function getProceduresByDependencyWithSuit(dependencyId: string): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Fetching procedures by dependency with real codes:', dependencyId)
    const supabase = getSupabaseClient()

    // Obtener información de contacto de la dependencia específica
    const { data: dependencyData, error: depError } = await supabase
      .from('dependencies')
      .select('id, name, code, contact_email, contact_phone, address')
      .eq('code', dependencyId)
      .eq('is_active', true)
      .single()

    if (depError) {
      console.error('Error fetching dependency contact info:', depError)
    }

    // Obtener trámites con códigos reales para esta dependencia
    const { data: tramitesData, error: tramitesError } = await supabase
      .from('procedures')
      .select(`
        id, name, description, cost, response_time, category, is_active,
        created_at, updated_at, codigo_tramite, has_cost, cost_description,
        subdependencies!inner(
          id, name, code,
          dependencies!inner(code)
        )
      `)
      .eq('is_active', true)
      .eq('subdependencies.dependencies.code', dependencyId)
      .order('name', { ascending: true })

    if (tramitesError) {
      console.error('Error fetching tramites for dependency:', tramitesError)
    }

    // Obtener OPAs con códigos reales para esta dependencia (usar tabla opas correcta)
    const { data: opasData, error: opasError } = await supabase
      .from('opas')
      .select(`
        id, code, name, description, created_at, updated_at,
        dependency_id, subdependency_id, has_cost, cost_description,
        suit_link, govco_link, is_active
      `)
      .eq('is_active', true)
      .order('code', { ascending: true })

    if (opasError) {
      console.error('Error fetching opas for dependency:', opasError)
    }

    // Filtrar OPAs por dependencia y obtener datos de subdependencias
    let filteredOpasData: any[] = []
    const subdependencyMapForDep = new Map()

    if (opasData && opasData.length > 0 && dependencyData) {
      // Obtener subdependencias de esta dependencia
      const { data: subdependencies } = await supabase
        .from('subdependencies')
        .select('id, name, code, dependency_id')
        .eq('dependency_id', dependencyData.id)

      subdependencies?.forEach(sub => subdependencyMapForDep.set(sub.id, sub))

      // Filtrar OPAs que pertenecen a subdependencias de esta dependencia
      filteredOpasData = opasData.filter(opa =>
        opa.subdependency_id && subdependencyMapForDep.has(opa.subdependency_id)
      )
    }

    const allProcedures: SuitEnhancedProcedure[] = []

    // Procesar trámites
    if (tramitesData) {
      tramitesData.forEach(item => {
        const subdependency = item.subdependencies

        const baseData = {
          id: item.id,
          name: item.name,
          codigo_tramite: item.codigo_tramite, // Usar código real de la base de datos
          code: undefined,
          description: item.description,
          cost: item.cost,
          response_time: item.response_time,
          category: item.category,
          has_cost: item.has_cost || false,
          cost_description: item.cost_description || '',
          suit_link: '', // No disponible en procedures
          govco_link: '', // No disponible en procedures
          procedure_type: 'tramite',
          dependency: {
            id: dependencyData?.id || '',
            name: dependencyData?.name || '',
            code: dependencyData?.code || '',
            contact_email: dependencyData?.contact_email || undefined,
            contact_phone: dependencyData?.contact_phone || undefined,
            address: dependencyData?.address || undefined
          },
          subdependency: subdependency ? {
            id: subdependency.id,
            name: subdependency.name,
            code: subdependency.code,
            dependency_id: dependencyData?.id || ''
          } : undefined,
          requirements: [],
          documents_required: [],
          process_steps: [],
          legal_framework: '',
          contact_info: null,
          online_available: false,
          tags: [],
          difficulty_level: 1,
          popularity_score: 0,
          is_active: item.is_active,
          created_at: item.created_at,
          updated_at: item.updated_at
        }

        allProcedures.push(enhanceProcedureWithSuit(baseData, undefined))
      })
    }

    // Procesar OPAs filtradas
    if (filteredOpasData) {
      filteredOpasData.forEach(item => {
        const subdependency = subdependencyMapForDep.get(item.subdependency_id)

        const baseData = {
          id: item.id,
          name: item.name, // Usar campo name real de la tabla opas
          codigo_tramite: undefined,
          code: item.code, // Usar código real de la base de datos
          description: item.description,
          cost: undefined,
          response_time: undefined,
          category: undefined,
          has_cost: item.has_cost || false, // Usar campo has_cost real de la tabla opas
          cost_description: item.cost_description || '', // Usar campo cost_description real
          suit_link: item.suit_link || '', // Usar campo suit_link real
          govco_link: item.govco_link || '', // Usar campo govco_link real
          procedure_type: 'opa',
          dependency: {
            id: dependencyData?.id || '',
            name: dependencyData?.name || '',
            code: dependencyData?.code || '',
            contact_email: dependencyData?.contact_email || undefined,
            contact_phone: dependencyData?.contact_phone || undefined,
            address: dependencyData?.address || undefined
          },
          subdependency: subdependency ? {
            id: subdependency.id,
            name: subdependency.name,
            code: subdependency.code,
            dependency_id: dependencyData?.id || ''
          } : undefined,
          requirements: [],
          documents_required: [],
          process_steps: [],
          legal_framework: '',
          contact_info: null,
          online_available: false,
          tags: [],
          difficulty_level: 1,
          popularity_score: 0,
          is_active: item.is_active,
          created_at: item.created_at,
          updated_at: item.updated_at
        }

        allProcedures.push(enhanceProcedureWithSuit(baseData, undefined))
      })
    }

    console.log(`Successfully processed ${allProcedures.length} procedures for dependency ${dependencyId} (${tramitesData?.length || 0} trámites, ${filteredOpasData?.length || 0} OPAs)`)
    return allProcedures
  } catch (error) {
    console.error('Error in getProceduresByDependencyWithSuit:', error)
    return []
  }
}
