import { createClient } from '@supabase/supabase-js'
import { SuitEnhancedProcedure, enhanceProcedureWithSuit, SuitData } from '@/types/suit-enhanced-procedure'
import { Database } from '@/lib/database.types'

/**
 * Crea un cliente de Supabase directo para el servicio
 */
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  return createClient<Database>(supabaseUrl, supabaseKey)
}

/**
 * Interface para datos consolidados de la vista all_procedures
 * Incluye tanto trámites como OPAs con campos unificados
 */
export interface ConsolidatedProcedureData {
  // Campos base de la vista all_procedures
  type: 'TRAMITE' | 'OPA'
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  dependency_name: string
  dependency_code: string
  subdependency_name?: string
  subdependency_code?: string
  is_active: boolean
  created_at: string
  updated_at: string

  // Campos específicos obtenidos via JOINs
  procedure_code?: string // codigo_tramite para trámites, code para OPAs
  has_cost?: boolean
  cost_description?: string
  suit_link?: string
  govco_link?: string

  // Datos SUIT (solo para trámites)
  descripcion_detallada?: string
  scraping_status?: string

  // Campos adicionales para compatibilidad
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  difficulty_level?: number
  popularity_score?: number
}

/**
 * Obtiene todos los procedimientos (trámites y OPAs) con sus datos SUIT asociados
 * Usa la vista all_procedures como fuente consolidada y complementa con datos específicos
 */
export async function getAllProceduresWithSuit(): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Starting getAllProceduresWithSuit...')
    const supabase = getSupabaseClient()

    // Usar directamente la consulta fallback que sabemos que funciona
    return await getAllProceduresWithSuitFallback()
  } catch (error) {
    console.error('Error in getAllProceduresWithSuit:', error)
    return []
  }
}

/**
 * Función fallback que usa consulta directa cuando la función RPC no está disponible
 */
async function getAllProceduresWithSuitFallback(): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Using simplified fallback method')
    const supabase = getSupabaseClient()

    // Consulta simplificada: obtener solo datos básicos de la vista
    const { data: rawData, error } = await supabase
      .from('all_procedures')
      .select('*')
      .eq('is_active', true)
      .order('name', { ascending: true })

    if (error) {
      console.error('Error in simplified fallback query:', error)
      return []
    }

    if (!rawData || rawData.length === 0) {
      console.log('No data returned from all_procedures view')
      return []
    }

    console.log(`Found ${rawData.length} procedures in all_procedures view`)

    // Obtener información de contacto de dependencias
    const { data: dependenciesData, error: depError } = await supabase
      .from('dependencies')
      .select('id, name, code, contact_email, contact_phone, address')
      .eq('is_active', true)

    if (depError) {
      console.error('Error fetching dependencies contact info:', depError)
    }

    // Crear un mapa de dependencias por código para acceso rápido
    const dependenciesMap = new Map()
    if (dependenciesData) {
      dependenciesData.forEach(dep => {
        dependenciesMap.set(dep.code, dep)
      })
    }

    // Convertir directamente sin consultas adicionales por ahora
    const enhancedProcedures = rawData.map(item => {
      // Obtener información de contacto de la dependencia
      const dependencyInfo = dependenciesMap.get(item.dependency_code)

      // Crear objeto base compatible con SuitEnhancedProcedure
      const baseData = {
        id: item.id,
        name: item.name,
        codigo_tramite: item.type === 'TRAMITE' ? `${item.dependency_code}-${item.subdependency_code || '000'}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}` : undefined,
        code: item.type === 'OPA' ? `${item.dependency_code}-${item.subdependency_code || '000'}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}` : undefined,
        description: item.description,
        cost: item.cost,
        response_time: item.response_time,
        category: item.category,
        has_cost: false, // Valor por defecto
        cost_description: '',
        suit_link: '',
        govco_link: '',
        procedure_type: item.type?.toLowerCase() || 'tramite',
        dependency: {
          id: dependencyInfo?.id || '',
          name: item.dependency_name,
          code: item.dependency_code,
          contact_email: dependencyInfo?.contact_email || undefined,
          contact_phone: dependencyInfo?.contact_phone || undefined,
          address: dependencyInfo?.address || undefined
        },
        subdependency: item.subdependency_name ? {
          id: '',
          name: item.subdependency_name,
          code: item.subdependency_code,
          dependency_id: ''
        } : undefined,
        requirements: [],
        documents_required: [],
        process_steps: [],
        legal_framework: '',
        contact_info: null,
        online_available: false,
        tags: [],
        difficulty_level: 1,
        popularity_score: 0,
        is_active: item.is_active,
        created_at: item.created_at,
        updated_at: item.updated_at
      }

      // No hay datos SUIT por ahora en esta versión simplificada
      return enhanceProcedureWithSuit(baseData, undefined)
    })

    console.log(`Successfully processed ${enhancedProcedures.length} procedures`)
    return enhancedProcedures

  } catch (error) {
    console.error('Error in getAllProceduresWithSuitFallback:', error)
    return []
  }
}

/**
 * Extrae el ficha_id de un enlace SUIT
 */
function extractFichaIdFromSuitLink(suitLink: string): string {
  try {
    const url = new URL(suitLink)
    const fiParam = url.searchParams.get('fi')
    return fiParam || ''
  } catch {
    return ''
  }
}

/**
 * Obtiene procedimientos (trámites y OPAs) con datos SUIT por dependencia
 * Usa la vista all_procedures como fuente consolidada
 */
export async function getProceduresByDependencyWithSuit(dependencyId: string): Promise<SuitEnhancedProcedure[]> {
  try {
    console.log('Fetching procedures by dependency:', dependencyId)
    const supabase = getSupabaseClient()

    // Consulta simplificada usando la vista all_procedures
    const { data: rawData, error } = await supabase
      .from('all_procedures')
      .select('*')
      .eq('dependency_code', dependencyId)
      .eq('is_active', true)
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching procedures by dependency:', error)
      return []
    }

    if (!rawData || rawData.length === 0) {
      console.log('No procedures found for dependency:', dependencyId)
      return []
    }

    console.log(`Found ${rawData.length} procedures for dependency ${dependencyId}`)

    // Obtener información de contacto de la dependencia específica
    const { data: dependencyData, error: depError } = await supabase
      .from('dependencies')
      .select('id, name, code, contact_email, contact_phone, address')
      .eq('code', dependencyId)
      .eq('is_active', true)
      .single()

    if (depError) {
      console.error('Error fetching dependency contact info:', depError)
    }

    // Convertir directamente sin consultas adicionales
    const enhancedProcedures = rawData.map(item => {
      const baseData = {
        id: item.id,
        name: item.name,
        codigo_tramite: item.type === 'TRAMITE' ? `${item.dependency_code}-${item.subdependency_code || '000'}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}` : undefined,
        code: item.type === 'OPA' ? `${item.dependency_code}-${item.subdependency_code || '000'}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}` : undefined,
        description: item.description,
        cost: item.cost,
        response_time: item.response_time,
        category: item.category,
        has_cost: false,
        cost_description: '',
        suit_link: '',
        govco_link: '',
        procedure_type: item.type?.toLowerCase() || 'tramite',
        dependency: {
          id: dependencyData?.id || '',
          name: item.dependency_name,
          code: item.dependency_code,
          contact_email: dependencyData?.contact_email || undefined,
          contact_phone: dependencyData?.contact_phone || undefined,
          address: dependencyData?.address || undefined
        },
        subdependency: item.subdependency_name ? {
          id: '',
          name: item.subdependency_name,
          code: item.subdependency_code,
          dependency_id: ''
        } : undefined,
        requirements: [],
        documents_required: [],
        process_steps: [],
        legal_framework: '',
        contact_info: null,
        online_available: false,
        tags: [],
        difficulty_level: 1,
        popularity_score: 0,
        is_active: item.is_active,
        created_at: item.created_at,
        updated_at: item.updated_at
      }

      return enhanceProcedureWithSuit(baseData, undefined)
    })

    console.log(`Successfully processed ${enhancedProcedures.length} enhanced procedures`)
    return enhancedProcedures
  } catch (error) {
    console.error('Error in getProceduresByDependencyWithSuit:', error)
    return []
  }
}
